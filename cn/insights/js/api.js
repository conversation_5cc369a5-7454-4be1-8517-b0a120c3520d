import {
  renderArticleItems,
  showError,
  setupFacetWP,
  showLoading,
} from './render.js'

function fetchArticles(page = 1, pageSize = 12) {
  // 确保每页最多显示12个项目
  const maxPageSize = 12
  const actualPageSize = Math.min(pageSize, maxPageSize)

  return fetch(
    `/prod-api/api/web/doc/news/list?pageNum=${page}&pageSize=${actualPageSize}`,
  )
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      // 验证返回的数据结构
      if (!data || typeof data !== 'object') {
        throw new Error('无效的响应数据格式')
      }

      if (!Array.isArray(data.rows)) {
        throw new Error('文章数据格式错误')
      }

      return data
    })
    .catch((error) => {
      console.error('获取文章失败:', error)
      throw error
    })
}

// 主要的数据加载函数
function loadArticles(topicsPromise, page = 1) {
  return fetchArticles(page)
    .then((data) => {
      const currentPage = page
      const total = data.total
      const totalPage = Math.ceil(total / 12)
      const articles = data.rows.map((item) => ({
        ...item,
        topic: topicsPromise.find((t) =>
          item.topic.split(',').includes(t.typeValue),
        )?.typeLabel,
      }))
      setupFacetWP(topicsPromise, {
        page: currentPage,
        total,
        totalPage,
      })

      if (Array.isArray(articles) && articles.length > 0) {
        renderArticleItems(articles)
      } else {
        showError('没有找到文章数据')
      }

      return articles
    })
    .catch((error) => {
      showError(error.message)
      throw error
    })
}

// 按页码加载文章
function loadArticlesByPage(page) {
  // 验证页码参数
  const pageNum = parseInt(page)
  if (!pageNum || pageNum < 1) {
    console.error('无效的页码:', page)
    showError('无效的页码')
    return Promise.reject(new Error('无效的页码'))
  }

  // 防止重复点击 - 检查是否正在加载
  const paginationFacet = document
    .querySelector('.facetwp-facet-pagination')
    .closest('.facetwp-facet')
  // if (paginationFacet && paginationFacet.classList.contains('is-loading')) {
  //   console.log('正在加载中，忽略重复请求')
  //   return Promise.resolve()
  // }

  // 添加加载状态
  // if (paginationFacet) {
  //   paginationFacet.classList.add('is-loading')
  // }

  showLoading()

  // 获取已缓存的topics数据，如果没有则重新获取
  const loadPromise = window.cachedTopics
    ? loadArticles(window.cachedTopics, pageNum)
    : getDocNewTopic().then((topics) => {
        window.cachedTopics = topics
        return loadArticles(topics, pageNum)
      })

  return loadPromise
    .catch((error) => {
      console.error('加载文章失败:', error)
      showError(error.message || '加载文章失败')
      throw error
    })
    .finally(() => {
      // 移除加载状态
      if (paginationFacet) {
        paginationFacet.classList.remove('is-loading')
      }
    })
}

// 获取新闻主题字典数据
function getDocNewTopic() {
  return fetch(`/prod-api/api/web/doc/news/type/list`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      const topics = data.data || []
      return topics
    })
    .catch((error) => {
      console.error('获取新闻主题失败:', error)
      throw error
    })
}

// 获取字典数据的通用函数
function getDic(dictType = 'service') {
  return fetch(`/prod-api/api/web/system/dict/data/type/${dictType}`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      return data.data || []
    })
    .catch((error) => {
      console.error(`获取字典数据失败 (${dictType}):`, error)
      throw error
    })
}

// 初始化页面数据
async function initializePage() {
  try {
    const topics = await getDocNewTopic()
    window.cachedTopics = topics // 缓存topics数据
    await loadArticles(topics)
  } catch (error) {
    console.error('页面初始化失败:', error)
  }
}

// 导出函数
export {
  loadArticles,
  loadArticlesByPage,
  getDocNewTopic,
  getDic,
  initializePage,
}
