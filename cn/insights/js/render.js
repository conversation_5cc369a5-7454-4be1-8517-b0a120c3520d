// Google Tag Manager
  ;(function (w, d, s, l, i) {
    w[l] = w[l] || []
    w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
    var f = d.getElementsByTagName(s)[0],
      j = d.createElement(s),
      dl = l != 'dataLayer' ? '&l=' + l : ''
    j.async = true
    j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl
    f.parentNode.insertBefore(j, f)
  })(window, document, 'script', 'dataLayer', 'GTM-W4DR9R')
}

// OneTrust Cookies Consent Notice
function OptanonWrapper() {}

// 动态渲染文章列表
function renderArticleItems(articles) {
  const container = document.querySelector('.facetwp-template .row')
  if (!container) {
    console.error('找不到文章容器')
    return
  }

  // 清空现有内容（保留 fwp-loop 注释）
  container.innerHTML = '<!--fwp-loop-->'

  // 遍历文章数据并生成HTML
  articles.forEach((article, index) => {
    const articleElement = createArticleElement(article)
    container.appendChild(articleElement)

    // 添加渐入动画效果
    setTimeout(() => {
      const articleItem = articleElement.querySelector('.crw-article-item')
      if (articleItem) {
        articleItem.classList.add('loaded')
      }
    }, index * 100) // 每个元素延迟100ms显示
  })
}

// 创建单个文章元素
function createArticleElement(article) {
  const colDiv = document.createElement('div')
  colDiv.className = 'col-lg-3'

  colDiv.innerHTML = `
            <div class="crw-article-item">
                <a href="${article.url || '#'}">
                    <span class="article-top">
                        <span class="article-img" style="background-image: url('https://crownwwcn.com/prod-api${
                          article.cover ||
                          '../wp-content/uploads/sites/7/2025/03/default-article.jpg'
                        }');"></span>
                        <span class="article-cat">${
                          article.topic || '未分类'
                        }</span>
                        <span class="article-title">${
                          article.title || '无标题'
                        }</span>
                    </span>
                    <span class="article-bottom">
                        <span class="btn-text">阅读更多 <i class="icon icon-arrow-button"></i></span>
                    </span>
                </a>
            </div>
        `

  return colDiv
}

// 显示加载状态
function showLoading() {
  const container = document.querySelector('.facetwp-template .row')
  if (container) {
    container.innerHTML = `
                <!--fwp-loop-->
                <div class="col-lg-12 text-center">
                    <div class="loading-spinner">
                        <p>正在加载文章...</p>
                    </div>
                </div>
            `
  }
}

// 显示错误状态
function showError(message) {
  const container = document.querySelector('.facetwp-template .row')
  if (container) {
    container.innerHTML = `
                <!--fwp-loop-->
                <div class="col-lg-12 text-center">
                    <div class="error-message">
                        <p>加载失败: ${message}</p>
                        <button onclick="loadArticles()" class="btn btn-primary">重试</button>
                    </div>
                </div>
            `
  }
}

// 生成分页HTML
function generatePaginationHTML(currentPage, totalPages) {
  if (totalPages <= 1) {
    return '<div class="facetwp-pager"></div>'
  }

  let paginationHTML = '<div class="facetwp-pager">'

  // 上一页按钮
  if (currentPage > 1) {
    paginationHTML += `<a class="facetwp-page prev" data-page="${
      currentPage - 1
    }">« 上一页</a>`
  }

  // 页码逻辑：显示当前页前后的页码
  let startPage = Math.max(1, currentPage - 2)
  let endPage = Math.min(totalPages, currentPage + 2)

  // 确保至少显示5个页码（如果总页数允许）
  if (endPage - startPage < 4) {
    if (startPage === 1) {
      endPage = Math.min(totalPages, startPage + 4)
    } else if (endPage === totalPages) {
      startPage = Math.max(1, endPage - 4)
    }
  }

  // 如果起始页不是1，显示第一页和省略号
  if (startPage > 1) {
    paginationHTML += `<a class="facetwp-page first" data-page="1">1</a>`
    if (startPage > 2) {
      paginationHTML += `<span class="facetwp-page dots">...</span>`
    }
  }

  // 页码
  for (let i = startPage; i <= endPage; i++) {
    const isActive = i === currentPage
    let pageClass = 'facetwp-page'
    if (isActive) {
      pageClass += ' active'
    }
    if (i === 1 && startPage === 1) {
      pageClass += ' first'
    }
    if (i === totalPages && endPage === totalPages) {
      pageClass += ' last'
    }
    paginationHTML += `<a class="${pageClass}" data-page="${i}">${i}</a>`
  }

  // 如果结束页不是最后一页，显示省略号和最后一页
  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      paginationHTML += `<span class="facetwp-page dots">...</span>`
    }
    paginationHTML += `<a class="facetwp-page last" data-page="${totalPages}">${totalPages}</a>`
  }

  // 下一页按钮
  if (currentPage < totalPages) {
    paginationHTML += `<a class="facetwp-page next" data-page="${
      currentPage + 1
    }">下一页 »</a>`
  }

  paginationHTML += '</div>'
  return paginationHTML
}

// 渲染分页到页面
function renderPagination(paginationHTML) {
  const paginationContainer = document.querySelector(
    '.facetwp-facet-pagination',
  )
  if (paginationContainer) {
    paginationContainer.innerHTML = paginationHTML

    // 添加分页点击事件监听
    setupPaginationEvents()
  }
}

// 设置分页点击事件
function setupPaginationEvents() {
  // 移除所有现有的分页事件监听器
  removePaginationEvents()

  const paginationContainer = document.querySelector(
    '.facetwp-facet-pagination',
  )
  if (!paginationContainer) return

  // 使用事件委托处理所有分页点击
  paginationContainer.addEventListener('click', handlePaginationClick, true)

  // 阻止分页容器内的其他事件冒泡
  paginationContainer.addEventListener('mousedown', preventOtherEvents, true)
  paginationContainer.addEventListener('mouseup', preventOtherEvents, true)
  paginationContainer.addEventListener('dblclick', preventOtherEvents, true)
  paginationContainer.addEventListener('contextmenu', preventOtherEvents, true)
}

// 处理分页点击事件
function handlePaginationClick(e) {
  // 阻止默认行为和事件冒泡
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()

  const target = e.target.closest('.facetwp-page')
  if (!target) return

  // 检查是否是有效的分页链接
  if (!target.hasAttribute('data-page')) return

  const page = parseInt(target.getAttribute('data-page'))

  // 验证页码有效性
  if (!page || page < 1) return

  // 检查是否是当前激活页或省略号
  if (
    target.classList.contains('active') ||
    target.classList.contains('dots')
  ) {
    return
  }

  // 检查是否正在加载中
  // if (target.closest('.facetwp-facet').classList.contains('is-loading')) {
  //   return
  // }

  // 添加点击效果
  target.classList.add('clicking')
  setTimeout(() => {
    target.classList.remove('clicking')
  }, 150)

  // 滚动到页面顶部
  scrollToTop()

  // 触发页面加载
  if (window.loadArticlesByPage) {
    window.loadArticlesByPage(page)
  } else {
    console.error('loadArticlesByPage 函数未定义')
  }
}

// 阻止其他事件
function preventOtherEvents(e) {
  const target = e.target.closest('.facetwp-page')
  if (target) {
    e.preventDefault()
    e.stopPropagation()
    e.stopImmediatePropagation()
  }
}

// 移除分页事件监听器
function removePaginationEvents() {
  const paginationContainer = document.querySelector(
    '.facetwp-facet-pagination',
  )
  if (paginationContainer) {
    // 克隆节点以移除所有事件监听器
    const newContainer = paginationContainer.cloneNode(true)
    paginationContainer.parentNode.replaceChild(
      newContainer,
      paginationContainer,
    )
  }
}

// 滚动到页面顶部
function scrollToTop() {
  const articlesSection = document.querySelector('.facetwp-template')
  if (articlesSection) {
    articlesSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  } else {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }
}

// 配置FacetWP和懒加载
function setupFacetWP(topics, { page, total, totalPage }) {
  // 动态加载 FacetWP 前端脚本
  const script = document.createElement('script')
  script.src =
    '../wp-content/plugins/facetwp/assets/js/dist/front.min7035.js?ver=4.3.4'
  script.async = true
  script.onload = function () {
    console.log('FacetWP 脚本加载成功')
  }
  script.onerror = function () {
    console.error('FacetWP 脚本加载失败')
  }
  document.head.appendChild(script)

  // 生成动态分页HTML
  const paginationHTML = generatePaginationHTML(page, totalPage)

  window.FWP_JSON = {
    prefix: '_',
    no_results_text: 'No results found',
    ajaxurl: 'https://www.crownrms.com/cn/wp-json/facetwp/v1/refresh',
    nonce: '6af19a93be',
    preload_data: {
      facets: {
        search:
          '<span class="facetwp-input-wrap"><i class="facetwp-icon" data-ts="\u641c\u7d22"></i><input type="text" class="facetwp-search" value="" placeholder="\u8f93\u5165\u5173\u952e\u5b57" autocomplete="off" /></span>',
        topics: `
        <select class="facetwp-dropdown"><option value="">\u6240\u6709\u4e3b\u9898<\/option>
        ${topics
          .map(
            (topic) =>
              `<option value="${topic.typeValue}" data-counter="${topic.newsCount}" class="d0">${topic.typeLabel}</option>`,
          )
          .join('')}
        </select>
        `,
        industries:
          '<select class="facetwp-dropdown"><option value="">\u6240\u6709\u884c\u4e1a</option></select>',
        pagination: paginationHTML,
      },
      template: '',
      settings: {
        debug: 'Enable debug mode in [Settings > FacetWP > Settings]',
        pager: {
          page: page,
          per_page: 12,
          total_rows: total,
          total_rows_unfiltered: total,
          total_pages: totalPage,
        },
        num_choices: { topics: 10, industries: 0 },
        labels: {
          search: 'Search',
          topics: 'Topics',
          industries: 'Industries',
          pagination: 'Pagination',
        },
        search: { auto_refresh: 'no' },
        topics: {
          placeholder: '\u6240\u6709\u4e3b\u9898',
          overflowText: '{n} selected',
          searchText: 'Search',
          noResultsText: 'No results found',
          operator: 'and',
        },
        industries: {
          placeholder: '\u6240\u6709\u884c\u4e1a',
          overflowText: '{n} selected',
          searchText: 'Search',
          noResultsText: 'No results found',
          operator: 'and',
        },
        pagination: {
          pager_type: 'numbers',
          scroll_target: '',
          scroll_offset: 0,
        },
      },
    },
  }
  window.FWP_HTTP = { get: [], uri: 'cn/insights', url_vars: [] }

  // 渲染分页到页面
  renderPagination(paginationHTML)

  // 设置主题下拉框事件监听器
  setupTopicsDropdownEvents()

  setupLazyLoad()
}

// 设置主题下拉框事件监听器
function setupTopicsDropdownEvents() {
  // 使用事件委托监听下拉框变化
  document.addEventListener('change', function (e) {
    // 检查是否是topics下拉框
    if (e.target.matches('.facetwp-facet-topics .facetwp-dropdown')) {
      const selectedValue = e.target.value
      console.log('主题下拉框选择变化:', selectedValue)

      // 如果选择了特定主题，根据主题筛选文章
      if (selectedValue) {
        handleTopicSelection(selectedValue)
      } else {
        // 如果选择"所有主题"，重新加载所有文章
        handleAllTopicsSelection()
      }
    }
  })
}

// 处理主题选择
function handleTopicSelection(topicValue) {
  console.log('处理主题选择:', topicValue)

  // 显示加载状态
  showLoading()

  // 根据选择的主题筛选文章
  filterArticlesByTopic(topicValue)
    .then((articles) => {
      if (articles && articles.length > 0) {
        renderArticleItems(articles)
      } else {
        showError('该主题下暂无文章')
      }
    })
    .catch((error) => {
      console.error('筛选文章失败:', error)
      showError('筛选文章失败: ' + error.message)
    })
}

// 处理"所有主题"选择
function handleAllTopicsSelection() {
  console.log('选择所有主题，重新加载文章')

  // 重新加载所有文章
  if (window.loadArticlesByPage) {
    window.loadArticlesByPage(1)
  }
}

// 根据主题筛选文章
async function filterArticlesByTopic(topicValue) {
  try {
    // 获取文章列表，这里可以添加主题筛选参数
    const response = await fetch(
      `/prod-api/api/web/doc/news/list?pageNum=1&pageSize=12&topic=${encodeURIComponent(
        topicValue,
      )}`,
    )

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data || !Array.isArray(data.rows)) {
      throw new Error('文章数据格式错误')
    }

    // 获取缓存的主题数据来映射主题名称
    const topics = window.cachedTopics || []

    // 处理文章数据，添加主题标签
    const articles = data.rows.map((item) => ({
      ...item,
      topic:
        topics.find(
          (t) => item.topic && item.topic.split(',').includes(t.typeValue),
        )?.typeLabel || '未分类',
    }))

    return articles
  } catch (error) {
    console.error('筛选文章失败:', error)
    throw error
  }
}

// 设置懒加载
function setupLazyLoad() {
  window.lazyLoadOptions = [
    {
      elements_selector:
        'img[data-lazy-src],.rocket-lazyload,iframe[data-lazy-src]',
      data_src: 'lazy-src',
      data_srcset: 'lazy-srcset',
      data_sizes: 'lazy-sizes',
      class_loading: 'lazyloading',
      class_loaded: 'lazyloaded',
      threshold: 300,
      callback_loaded: function (element) {
        if (
          element.tagName === 'IFRAME' &&
          element.dataset.rocketLazyload == 'fitvidscompatible'
        ) {
          if (element.classList.contains('lazyloaded')) {
            if (typeof window.jQuery != 'undefined') {
              if (jQuery.fn.fitVids) {
                jQuery(element).parent().fitVids()
              }
            }
          }
        }
      },
    },
    {
      elements_selector: '.rocket-lazyload',
      data_src: 'lazy-src',
      data_srcset: 'lazy-srcset',
      data_sizes: 'lazy-sizes',
      class_loading: 'lazyloading',
      class_loaded: 'lazyloaded',
      threshold: 300,
    },
  ]

  window.addEventListener(
    'LazyLoad::Initialized',
    function (e) {
      var lazyLoadInstance = e.detail.instance
      if (window.MutationObserver) {
        var observer = new MutationObserver(function (mutations) {
          var image_count = 0
          var iframe_count = 0
          var rocketlazy_count = 0
          mutations.forEach(function (mutation) {
            for (var i = 0; i < mutation.addedNodes.length; i++) {
              if (
                typeof mutation.addedNodes[i].getElementsByTagName !==
                'function'
              ) {
                continue
              }
              if (
                typeof mutation.addedNodes[i].getElementsByClassName !==
                'function'
              ) {
                continue
              }
              images = mutation.addedNodes[i].getElementsByTagName('img')
              is_image = mutation.addedNodes[i].tagName == 'IMG'
              iframes = mutation.addedNodes[i].getElementsByTagName('iframe')
              is_iframe = mutation.addedNodes[i].tagName == 'IFRAME'
              rocket_lazy =
                mutation.addedNodes[i].getElementsByClassName('rocket-lazyload')
              image_count += images.length
              iframe_count += iframes.length
              rocketlazy_count += rocket_lazy.length
              if (is_image) {
                image_count += 1
              }
              if (is_iframe) {
                iframe_count += 1
              }
            }
          })
          if (image_count > 0 || iframe_count > 0 || rocketlazy_count > 0) {
            lazyLoadInstance.update()
          }
        })
        var b = document.getElementsByTagName('body')[0]
        var config = { childList: !0, subtree: !0 }
        observer.observe(b, config)
      }
    },
    !1,
  )
}

// 导出函数
export {
  renderArticleItems,
  createArticleElement,
  showLoading,
  showError,
  setupFacetWP,
  generatePaginationHTML,
  renderPagination,
  setupPaginationEvents,
  handlePaginationClick,
  preventOtherEvents,
  removePaginationEvents,
  scrollToTop,
  setupTopicsDropdownEvents,
  handleTopicSelection,
  handleAllTopicsSelection,
  filterArticlesByTopic,
}
