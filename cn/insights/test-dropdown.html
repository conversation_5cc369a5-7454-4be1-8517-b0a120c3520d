<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试主题下拉框功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .facetwp-facet-topics {
            margin: 20px 0;
        }
        .facetwp-dropdown {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .loading-spinner {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error-message {
            color: #d32f2f;
            padding: 10px;
            background-color: #ffebee;
            border-radius: 4px;
            margin: 10px 0;
        }
        .article-list {
            margin-top: 20px;
        }
        .article-item {
            padding: 10px;
            margin: 5px 0;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 3px solid #2196f3;
        }
        .log-section {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            font-family: monospace;
            font-size: 12px;
            margin: 2px 0;
            padding: 2px 5px;
            background-color: white;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>主题下拉框功能测试</h1>
        
        <div class="test-section">
            <h3>模拟主题下拉框</h3>
            <div class="facetwp-facet facetwp-facet-topics">
                <select class="facetwp-dropdown">
                    <option value="">所有主题</option>
                    <option value="信息管理">信息管理</option>
                    <option value="保险">保险</option>
                    <option value="技术创新">技术创新</option>
                    <option value="行业趋势">行业趋势</option>
                </select>
            </div>
            <p><strong>说明：</strong>选择不同的主题选项来测试下拉框事件处理功能。</p>
        </div>

        <div class="test-section">
            <h3>文章显示区域</h3>
            <div id="article-container">
                <div class="facetwp-template">
                    <div class="row">
                        <!--fwp-loop-->
                        <p>请选择主题来加载相关文章...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>控制台日志</h3>
            <div id="log-container" class="log-section">
                <div class="log-entry">等待用户操作...</div>
            </div>
            <button onclick="clearLogs()">清空日志</button>
        </div>
    </div>

    <script type="module">
        // 模拟必要的全局变量和函数
        window.cachedTopics = [
            { typeValue: '信息管理', typeLabel: '信息管理', newsCount: 5 },
            { typeValue: '保险', typeLabel: '保险', newsCount: 3 },
            { typeValue: '技术创新', typeLabel: '技术创新', newsCount: 7 },
            { typeValue: '行业趋势', typeLabel: '行业趋势', newsCount: 4 }
        ];

        // 模拟loadArticlesByPage函数
        window.loadArticlesByPage = function(page) {
            addLog(`调用 loadArticlesByPage(${page})`);
            showMockArticles('所有主题');
        };

        // 导入我们的函数
        import { 
            setupTopicsDropdownEvents, 
            showLoading, 
            showError,
            renderArticleItems 
        } from './js/render.js';

        // 设置事件监听器
        setupTopicsDropdownEvents();

        // 日志功能
        function addLog(message) {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        window.clearLogs = function() {
            document.getElementById('log-container').innerHTML = '<div class="log-entry">日志已清空</div>';
        };

        // 模拟显示文章
        function showMockArticles(topic) {
            const container = document.querySelector('#article-container .row');
            container.innerHTML = `
                <!--fwp-loop-->
                <div class="article-item">
                    <h4>模拟文章 1 - ${topic}</h4>
                    <p>这是一篇关于${topic}的模拟文章内容...</p>
                </div>
                <div class="article-item">
                    <h4>模拟文章 2 - ${topic}</h4>
                    <p>这是另一篇关于${topic}的模拟文章内容...</p>
                </div>
            `;
            addLog(`显示了${topic}相关的模拟文章`);
        }

        // 重写console.log来显示在页面上
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addLog(args.join(' '));
        };

        const originalConsoleError = console.error;
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addLog('ERROR: ' + args.join(' '));
        };

        // 模拟fetch函数来测试API调用
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            addLog(`模拟API调用: ${url}`);
            
            // 模拟API响应
            return new Promise((resolve) => {
                setTimeout(() => {
                    const mockData = {
                        rows: [
                            {
                                id: 1,
                                title: '模拟文章标题 1',
                                topic: '信息管理',
                                cover: '/mock-image-1.jpg',
                                url: '#'
                            },
                            {
                                id: 2,
                                title: '模拟文章标题 2',
                                topic: '信息管理',
                                cover: '/mock-image-2.jpg',
                                url: '#'
                            }
                        ],
                        total: 2
                    };
                    
                    resolve({
                        ok: true,
                        json: () => Promise.resolve(mockData)
                    });
                }, 500); // 模拟网络延迟
            });
        };

        addLog('测试页面已加载，主题下拉框事件监听器已设置');
    </script>
</body>
</html>
